# 测试设置指南

## 快速测试步骤

### 1. 安装Node.js（如果还没有）
- 访问 https://nodejs.org
- 下载并安装LTS版本
- 重启命令行

### 2. 安装依赖
```bash
npm install
```

### 3. 启动开发服务器
```bash
npm run dev
```

### 4. 测试视频功能
- 应用会自动加载提供的B站视频URL
- 可以在视频预览区域输入新的视频URL
- 按Enter键加载新视频

### 5. 测试拖拽功能
- 拖拽右侧面板的左边缘调整宽度
- 拖拽时间轴上方的边缘调整高度

### 6. 测试快捷键
- V键：选择工具
- C键：刀片工具
- 空格：播放/暂停
- Delete：删除选中镜头

## 已修复的问题

✅ 颜色方案：蓝色 → 绿色
✅ 布局调整：时间轴全宽，视频区域更小
✅ 可拖拽分割线：垂直和水平
✅ 时间轴高度增加：更好的缩略图显示
✅ 音量滑块：绿色主题，可拖拽
✅ 视频URL支持：可加载真实视频
✅ 所有蓝色按钮已更新为绿色

## 当前功能状态

🎬 **视频播放**：支持真实视频URL
🎨 **界面主题**：绿色专业主题
📐 **布局调整**：可拖拽分割线
⌨️ **快捷键**：完整支持
🎵 **音量控制**：绿色滑块，可调节
📱 **响应式**：适配不同屏幕
