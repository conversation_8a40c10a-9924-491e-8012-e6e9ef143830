{"hash": "306e6c52", "browserHash": "7210ef7d", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "362d590e", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "f937e67c", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "98211754", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "24aeceb8", "needsInterop": true}, "@reduxjs/toolkit": {"src": "../../@reduxjs/toolkit/dist/redux-toolkit.esm.js", "file": "@reduxjs_toolkit.js", "fileHash": "2b7d4511", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "b415ed0f", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "c1c90fb7", "needsInterop": true}, "react-redux": {"src": "../../react-redux/es/index.js", "file": "react-redux.js", "fileHash": "46cccdb3", "needsInterop": false}}, "chunks": {"chunk-WALXKXZM": {"file": "chunk-WALXKXZM.js"}, "chunk-WQMOH32Y": {"file": "chunk-WQMOH32Y.js"}, "chunk-5WWUZCGV": {"file": "chunk-5WWUZCGV.js"}}}