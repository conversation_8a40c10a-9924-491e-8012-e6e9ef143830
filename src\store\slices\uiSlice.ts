import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface UiState {
  activeTool: 'select' | 'blade' | 'merge';
  rightPanelTab: 'shots' | 'inspector';
  isTimelineInteracting: boolean;
}

const initialState: UiState = {
  activeTool: 'select',
  rightPanelTab: 'shots',
  isTimelineInteracting: false,
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    setActiveTool: (state, action: PayloadAction<'select' | 'blade' | 'merge'>) => {
      state.activeTool = action.payload;
    },
    setRightPanelTab: (state, action: PayloadAction<'shots' | 'inspector'>) => {
      state.rightPanelTab = action.payload;
    },
    setTimelineInteracting: (state, action: PayloadAction<boolean>) => {
      state.isTimelineInteracting = action.payload;
    },
  },
});

export const {
  setActiveTool,
  setRightPanelTab,
  setTimelineInteracting,
} = uiSlice.actions;

export default uiSlice.reducer;
