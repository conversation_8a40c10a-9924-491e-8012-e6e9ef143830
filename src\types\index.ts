export interface Shot {
  id: string;
  startTime: number;
  endTime: number;
  tags: string[];
  description: string;
  characters?: string[];
  thumbnail?: string;
}

export interface Subtitle {
  id: string;
  startTime: number;
  endTime: number;
  text: string;
}

export interface VideoState {
  url: string;
  duration: number;
  currentTime: number;
  isPlaying: boolean;
  volume: number;
  subtitles: Subtitle[];
  showSubtitles: boolean;
}

export interface TimelineState {
  zoom: number;
  scrollLeft: number;
  selectedShotId: string | null;
  playheadPosition: number;
}

export interface AppState {
  shots: Shot[];
  video: VideoState;
  timeline: TimelineState;
  activeTool: 'select' | 'blade';
  rightPanelTab: 'shots' | 'inspector';
}

export interface TimelineInteraction {
  isDragging: boolean;
  dragType: 'playhead' | 'shot' | 'shot-edge' | 'timeline' | null;
  dragStartX: number;
  dragStartTime: number;
  dragShotId?: string;
  dragEdge?: 'start' | 'end';
}
