import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { List, Settings } from 'lucide-react';
import { RootState } from '../store/store';
import { setRightPanelTab } from '../store/slices/uiSlice';
import ShotList from './ShotList';
import Inspector from './Inspector';

const RightPanel: React.FC = () => {
  const dispatch = useDispatch();
  const { rightPanelTab } = useSelector((state: RootState) => state.ui);

  return (
    <div className="h-full bg-gray-800 flex flex-col">
      {/* Tab 导航 */}
      <div className="flex border-b border-gray-700">
        <button
          className={`flex-1 flex items-center justify-center gap-2 py-3 px-4 text-sm font-medium transition-colors ${
            rightPanelTab === 'shots'
              ? 'bg-gray-700 text-white border-b-2 border-green-500'
              : 'text-gray-400 hover:text-white hover:bg-gray-600'
          }`}
          onClick={() => dispatch(setRightPanelTab('shots'))}
        >
          <List size={16} />
          镜头列表
        </button>
        
        <button
          className={`flex-1 flex items-center justify-center gap-2 py-3 px-4 text-sm font-medium transition-colors ${
            rightPanelTab === 'inspector'
              ? 'bg-gray-700 text-white border-b-2 border-green-500'
              : 'text-gray-400 hover:text-white hover:bg-gray-600'
          }`}
          onClick={() => dispatch(setRightPanelTab('inspector'))}
        >
          <Settings size={16} />
          检查器
        </button>
      </div>

      {/* Tab 内容 */}
      <div className="flex-1 overflow-hidden">
        {rightPanelTab === 'shots' && <ShotList />}
        {rightPanelTab === 'inspector' && <Inspector />}
      </div>
    </div>
  );
};

export default RightPanel;
