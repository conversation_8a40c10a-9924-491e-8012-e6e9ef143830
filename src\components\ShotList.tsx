import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Play, Clock } from 'lucide-react';
import { RootState } from '../store/store';
import { setSelectedShotId } from '../store/slices/timelineSlice';
import { setCurrentTime } from '../store/slices/videoSlice';
import { setRightPanelTab } from '../store/slices/uiSlice';

const ShotList: React.FC = () => {
  const dispatch = useDispatch();
  const { shots } = useSelector((state: RootState) => state.shots);
  const { selectedShotId } = useSelector((state: RootState) => state.timeline);

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const formatDuration = (startTime: number, endTime: number) => {
    const duration = endTime - startTime;
    return formatTime(duration);
  };

  const handleShotClick = (shotId: string, startTime: number) => {
    dispatch(setSelectedShotId(shotId));
    dispatch(setCurrentTime(startTime));
    dispatch(setRightPanelTab('inspector'));
  };

  const handlePlayShot = (e: React.MouseEvent, startTime: number) => {
    e.stopPropagation();
    dispatch(setCurrentTime(startTime));
  };

  return (
    <div className="h-full flex flex-col">
      {/* 列表头部 */}
      <div className="p-4 border-b border-gray-700">
        <h3 className="text-lg font-semibold text-white">镜头列表</h3>
        <p className="text-sm text-gray-400 mt-1">共 {shots.length} 个镜头</p>
      </div>

      {/* 镜头列表 */}
      <div className="flex-1 overflow-y-auto">
        {shots.length === 0 ? (
          <div className="p-4 text-center text-gray-400">
            <p>暂无镜头</p>
            <p className="text-sm mt-2">使用刀片工具分割视频创建镜头</p>
          </div>
        ) : (
          <div className="space-y-2 p-4">
            {shots.map((shot, index) => (
              <div
                key={shot.id}
                className={`p-3 rounded-lg border cursor-pointer transition-all duration-200 ${
                  selectedShotId === shot.id
                    ? 'bg-green-600 border-green-500 shadow-lg'
                    : 'bg-gray-700 border-gray-600 hover:bg-gray-600 hover:border-gray-500'
                }`}
                onClick={() => handleShotClick(shot.id, shot.startTime)}
              >
                {/* 镜头头部 */}
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-white">
                      镜头 {index + 1}
                    </span>
                    <button
                      onClick={(e) => handlePlayShot(e, shot.startTime)}
                      className="p-1 rounded hover:bg-gray-600 transition-colors"
                      title="播放此镜头"
                    >
                      <Play size={12} />
                    </button>
                  </div>
                  
                  <div className="flex items-center gap-1 text-xs text-gray-300">
                    <Clock size={12} />
                    {formatDuration(shot.startTime, shot.endTime)}
                  </div>
                </div>

                {/* 时间信息 */}
                <div className="text-xs text-gray-300 mb-2">
                  {formatTime(shot.startTime)} - {formatTime(shot.endTime)}
                </div>

                {/* 缩略图占位符 */}
                <div className="w-full h-16 bg-gray-600 rounded mb-2 flex items-center justify-center">
                  <span className="text-xs text-gray-400">缩略图</span>
                </div>

                {/* 标签 */}
                {shot.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mb-2">
                    {shot.tags.map((tag, tagIndex) => (
                      <span
                        key={tagIndex}
                        className="px-2 py-1 text-xs bg-green-500 text-white rounded"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                )}

                {/* 描述 */}
                {shot.description && (
                  <p className="text-xs text-gray-300 line-clamp-2">
                    {shot.description}
                  </p>
                )}

                {/* 人物信息 */}
                {shot.characters && shot.characters.length > 0 && (
                  <div className="mt-2 text-xs text-gray-400">
                    人物: {shot.characters.join(', ')}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ShotList;
