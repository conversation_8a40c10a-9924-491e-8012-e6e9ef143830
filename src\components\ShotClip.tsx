import React from 'react';
import { Shot } from '../types';

interface ShotClipProps {
  shot: Shot;
  isSelected: boolean;
  timeToPixel: (time: number) => number;
  scrollLeft: number;
  onClick: (e: React.MouseEvent) => void;
  onMouseDown: (e: React.MouseEvent, dragType: 'move' | 'resize-start' | 'resize-end') => void;
  activeTool: 'select' | 'blade';
}

const ShotClip: React.FC<ShotClipProps> = ({
  shot,
  isSelected,
  timeToPixel,
  scrollLeft,
  onClick,
  onMouseDown,
  activeTool,
}) => {
  const startPixel = timeToPixel(shot.startTime) - scrollLeft;
  const endPixel = timeToPixel(shot.endTime) - scrollLeft;
  const width = endPixel - startPixel;

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // 生成缩略图网格（模拟）
  const thumbnailCount = Math.max(1, Math.floor(width / 60)); // 每60像素一个缩略图
  const thumbnails = Array.from({ length: thumbnailCount }, (_, i) => i);

  return (
    <div
      className={`shot-clip ${isSelected ? 'selected' : ''} h-28 top-2`}
      style={{
        left: startPixel,
        width: Math.max(width, 20), // 最小宽度20像素
        cursor: activeTool === 'blade' ? 'crosshair' : 'pointer',
      }}
      onClick={onClick}
      onMouseDown={(e) => onMouseDown(e, 'move')}
    >
      {/* 缩略图网格 */}
      <div className="flex h-full overflow-hidden">
        {thumbnails.map((index) => (
          <div
            key={index}
            className="flex-shrink-0 w-14 h-full bg-gray-600 border-r border-gray-500 flex items-center justify-center"
            style={{ minWidth: '56px' }}
          >
            <div className="w-10 h-8 bg-gray-700 rounded flex items-center justify-center">
              <span className="text-xs text-gray-400">#{index + 1}</span>
            </div>
          </div>
        ))}
      </div>

      {/* 镜头信息覆盖层 */}
      <div className="absolute inset-0 flex flex-col justify-between p-1 pointer-events-none">
        {/* 顶部信息 */}
        <div className="flex justify-between items-start">
          <span className="text-xs font-medium text-white bg-black bg-opacity-50 px-1 rounded">
            Shot {shot.id}
          </span>
          {shot.tags.length > 0 && (
            <div className="flex gap-1">
              {shot.tags.slice(0, 2).map((tag, index) => (
                <span
                  key={index}
                  className="text-xs bg-green-500 text-white px-1 rounded"
                >
                  {tag}
                </span>
              ))}
              {shot.tags.length > 2 && (
                <span className="text-xs bg-gray-500 text-white px-1 rounded">
                  +{shot.tags.length - 2}
                </span>
              )}
            </div>
          )}
        </div>

        {/* 底部时间信息 */}
        <div className="text-xs text-white bg-black bg-opacity-50 px-1 rounded self-start">
          {formatTime(shot.startTime)} - {formatTime(shot.endTime)}
        </div>
      </div>

      {/* 调整手柄 */}
      {activeTool === 'select' && (
        <>
          {/* 左边缘调整手柄 */}
          <div
            className="absolute left-0 top-0 w-2 h-full bg-green-400 opacity-0 hover:opacity-100 cursor-ew-resize transition-opacity"
            onMouseDown={(e) => {
              e.stopPropagation();
              onMouseDown(e, 'resize-start');
            }}
          />

          {/* 右边缘调整手柄 */}
          <div
            className="absolute right-0 top-0 w-2 h-full bg-green-400 opacity-0 hover:opacity-100 cursor-ew-resize transition-opacity"
            onMouseDown={(e) => {
              e.stopPropagation();
              onMouseDown(e, 'resize-end');
            }}
          />
        </>
      )}

      {/* 选中状态的边框 */}
      {isSelected && (
        <div className="absolute inset-0 border-2 border-green-300 rounded pointer-events-none" />
      )}
    </div>
  );
};

export default ShotClip;
