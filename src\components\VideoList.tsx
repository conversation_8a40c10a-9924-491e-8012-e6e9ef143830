import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Play, Clock, Video } from 'lucide-react';
import { RootState } from '../store/store';
import { setVideoUrl, setCurrentTime } from '../store/slices/videoSlice';

interface VideoItem {
  id: string;
  title: string;
  url: string;
  duration: number;
  thumbnail?: string;
}

const VideoList: React.FC = () => {
  const dispatch = useDispatch();
  const { url: currentVideoUrl } = useSelector((state: RootState) => state.video);

  // 视频数据
  const videos: VideoItem[] = [
    {
      id: '1',
      title: '本地视频',
      url: '/1.mp4',
      duration: 300,
    }
  ];

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleVideoSelect = (video: VideoItem) => {
    dispatch(setVideoUrl(video.url));
    dispatch(setCurrentTime(0));
  };

  return (
    <div className="h-full flex flex-col">
      {/* 列表头部 */}
      <div className="p-4 border-b border-gray-700">
        <h3 className="text-lg font-semibold text-white">视频列表</h3>
        <p className="text-sm text-gray-400 mt-1">共 {videos.length} 个视频</p>
      </div>

      {/* 视频列表 */}
      <div className="flex-1 overflow-y-auto">
        <div className="space-y-2 p-4">
          {videos.map((video) => (
            <div
              key={video.id}
              className={`p-3 rounded-lg border cursor-pointer transition-all duration-200 ${
                currentVideoUrl === video.url
                  ? 'bg-green-600 border-green-500 shadow-lg'
                  : 'bg-gray-700 border-gray-600 hover:bg-gray-600 hover:border-gray-500'
              }`}
              onClick={() => handleVideoSelect(video)}
            >
              {/* 视频头部 */}
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Video size={16} className="text-gray-400" />
                  <span className="text-sm font-medium text-white">
                    {video.title}
                  </span>
                </div>
                
                <div className="flex items-center gap-1 text-xs text-gray-300">
                  <Clock size={12} />
                  {formatTime(video.duration)}
                </div>
              </div>

              {/* 缩略图占位符 */}
              <div className="w-full h-16 bg-gray-600 rounded mb-2 flex items-center justify-center">
                <span className="text-xs text-gray-400">视频缩略图</span>
              </div>

              {/* 播放按钮 */}
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-400">
                  本地视频
                </span>
                
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleVideoSelect(video);
                  }}
                  className="p-1 rounded hover:bg-gray-600 transition-colors"
                  title="播放此视频"
                >
                  <Play size={12} />
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 添加视频按钮 */}
      <div className="p-4 border-t border-gray-700">
        <button className="w-full p-2 bg-green-600 hover:bg-green-500 text-white rounded-lg transition-colors text-sm">
          + 添加视频
        </button>
      </div>
    </div>
  );
};

export default VideoList;
