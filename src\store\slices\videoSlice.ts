import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { VideoState, Subtitle } from '../../types';

const initialState: VideoState = {
  url: '/1.mp4',
  duration: 300, // 5分钟的模拟视频
  currentTime: 0,
  isPlaying: false,
  volume: 1,
  subtitles: [
    // 示例字幕数据
    { id: '1', startTime: 0, endTime: 3, text: '欢迎来到视频编辑器' },
    { id: '2', startTime: 3, endTime: 6, text: '这是一个功能强大的视频剪辑工具' },
    { id: '3', startTime: 6, endTime: 10, text: '您可以在这里进行视频编辑和字幕管理' },
    { id: '4', startTime: 15, endTime: 18, text: '支持多种视频格式和字幕功能' },
    { id: '5', startTime: 25, endTime: 30, text: '让我们开始您的创作之旅吧！' },
  ],
  showSubtitles: true,
};

const videoSlice = createSlice({
  name: 'video',
  initialState,
  reducers: {
    setVideoUrl: (state, action: PayloadAction<string>) => {
      state.url = action.payload;
    },
    setDuration: (state, action: PayloadAction<number>) => {
      state.duration = action.payload;
    },
    setCurrentTime: (state, action: PayloadAction<number>) => {
      state.currentTime = Math.max(0, Math.min(action.payload, state.duration));
    },
    setIsPlaying: (state, action: PayloadAction<boolean>) => {
      state.isPlaying = action.payload;
    },
    setVolume: (state, action: PayloadAction<number>) => {
      state.volume = Math.max(0, Math.min(action.payload, 1));
    },
    togglePlayPause: (state) => {
      state.isPlaying = !state.isPlaying;
    },
    setSubtitles: (state, action: PayloadAction<Subtitle[]>) => {
      state.subtitles = action.payload;
    },
    addSubtitle: (state, action: PayloadAction<Subtitle>) => {
      state.subtitles.push(action.payload);
    },
    updateSubtitle: (state, action: PayloadAction<{ id: string; subtitle: Partial<Subtitle> }>) => {
      const index = state.subtitles.findIndex(sub => sub.id === action.payload.id);
      if (index !== -1) {
        state.subtitles[index] = { ...state.subtitles[index], ...action.payload.subtitle };
      }
    },
    removeSubtitle: (state, action: PayloadAction<string>) => {
      state.subtitles = state.subtitles.filter(sub => sub.id !== action.payload);
    },
    toggleSubtitles: (state) => {
      state.showSubtitles = !state.showSubtitles;
    },
  },
});

export const {
  setVideoUrl,
  setDuration,
  setCurrentTime,
  setIsPlaying,
  setVolume,
  togglePlayPause,
  setSubtitles,
  addSubtitle,
  updateSubtitle,
  removeSubtitle,
  toggleSubtitles,
} = videoSlice.actions;

export default videoSlice.reducer;
