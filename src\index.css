@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义滑块样式 */
.slider-green {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

.slider-green::-webkit-slider-track {
  background: #374151;
  height: 8px;
  border-radius: 4px;
}

.slider-green::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: #10b981;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

.slider-green::-webkit-slider-thumb:hover {
  background: #34d399;
  transform: scale(1.1);
}

.slider-green::-moz-range-track {
  background: #374151;
  height: 8px;
  border-radius: 4px;
  border: none;
}

.slider-green::-moz-range-thumb {
  background: #10b981;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

.slider-green::-moz-range-thumb:hover {
  background: #34d399;
  transform: scale(1.1);
}

@layer base {
  * {
    @apply border-gray-700;
  }
  
  body {
    @apply bg-gray-900 text-white font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .timeline-track {
    @apply relative bg-gray-800 border border-gray-700 rounded-lg overflow-hidden;
  }
  
  .shot-clip {
    @apply absolute top-0 bg-gradient-to-r from-green-600 to-green-500 border border-green-400 rounded cursor-pointer transition-all duration-200;
  }

  .shot-clip:hover {
    @apply from-green-500 to-green-400 border-green-300 shadow-lg;
  }

  .shot-clip.selected {
    @apply from-green-400 to-green-300 border-green-200 shadow-xl ring-2 ring-green-300;
  }
  
  .playhead {
    @apply absolute top-0 w-0.5 bg-red-500 z-50 pointer-events-none;
  }
  
  .time-ruler {
    @apply bg-gray-800 border-b border-gray-700 text-xs text-gray-400;
  }
  
  .tool-button {
    @apply p-2 rounded-lg bg-gray-700 hover:bg-gray-600 transition-colors duration-200 border border-gray-600;
  }
  
  .tool-button.active {
    @apply bg-green-600 hover:bg-green-500 border-green-500;
  }

  /* 音量滑块样式 */
  input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    background: transparent;
    cursor: pointer;
  }

  input[type="range"]::-webkit-slider-track {
    background: #374151;
    height: 8px;
    border-radius: 4px;
  }

  input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    background: #10b981;
    height: 16px;
    width: 16px;
    border-radius: 50%;
    cursor: pointer;
  }

  input[type="range"]::-moz-range-track {
    background: #374151;
    height: 8px;
    border-radius: 4px;
    border: none;
  }

  input[type="range"]::-moz-range-thumb {
    background: #10b981;
    height: 16px;
    width: 16px;
    border-radius: 50%;
    cursor: pointer;
    border: none;
  }
}
