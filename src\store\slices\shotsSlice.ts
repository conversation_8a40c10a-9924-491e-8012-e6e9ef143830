import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Shot } from '../../types';

interface ShotsState {
  shots: Shot[];
}

// 模拟初始镜头数据
const initialState: ShotsState = {
  shots: [
    {
      id: '1',
      startTime: 0,
      endTime: 45,
      tags: ['开场', '介绍'],
      description: '视频开场介绍场景',
      characters: ['主角A'],
    },
    {
      id: '2',
      startTime: 45,
      endTime: 120,
      tags: ['对话', '室内'],
      description: '主角与配角的对话场景',
      characters: ['主角A', '配角B'],
    },
    {
      id: '3',
      startTime: 120,
      endTime: 200,
      tags: ['动作', '户外'],
      description: '户外追逐场景',
      characters: ['主角A'],
    },
  ],
};

const shotsSlice = createSlice({
  name: 'shots',
  initialState,
  reducers: {
    addShot: (state, action: PayloadAction<Omit<Shot, 'id'>>) => {
      const newShot: Shot = {
        ...action.payload,
        id: Date.now().toString(),
      };
      state.shots.push(newShot);
      state.shots.sort((a, b) => a.startTime - b.startTime);
    },
    
    updateShot: (state, action: PayloadAction<{ id: string; updates: Partial<Shot> }>) => {
      const { id, updates } = action.payload;
      const shotIndex = state.shots.findIndex(shot => shot.id === id);
      if (shotIndex !== -1) {
        state.shots[shotIndex] = { ...state.shots[shotIndex], ...updates };
        state.shots.sort((a, b) => a.startTime - b.startTime);
      }
    },
    
    deleteShot: (state, action: PayloadAction<string>) => {
      state.shots = state.shots.filter(shot => shot.id !== action.payload);
    },

    mergeShots: (state, action: PayloadAction<string[]>) => {
      const shotIds = action.payload;
      if (shotIds.length < 2) return;

      // 按时间排序要合并的镜头
      const shotsToMerge = state.shots
        .filter(shot => shotIds.includes(shot.id))
        .sort((a, b) => a.startTime - b.startTime);

      if (shotsToMerge.length < 2) return;

      // 创建合并后的镜头
      const mergedShot: Shot = {
        id: Date.now().toString(),
        startTime: shotsToMerge[0].startTime,
        endTime: shotsToMerge[shotsToMerge.length - 1].endTime,
        tags: [...new Set(shotsToMerge.flatMap(shot => shot.tags))], // 去重合并标签
        description: shotsToMerge.map(shot => shot.description).filter(Boolean).join(' | '),
        characters: [...new Set(shotsToMerge.flatMap(shot => shot.characters || []))], // 去重合并人物
      };

      // 移除原镜头并添加合并后的镜头
      state.shots = state.shots.filter(shot => !shotIds.includes(shot.id));
      state.shots.push(mergedShot);
      state.shots.sort((a, b) => a.startTime - b.startTime);
    },
    
    splitShot: (state, action: PayloadAction<{ shotId: string; splitTime: number }>) => {
      const { shotId, splitTime } = action.payload;
      const shotIndex = state.shots.findIndex(shot => shot.id === shotId);
      
      if (shotIndex !== -1) {
        const originalShot = state.shots[shotIndex];
        
        if (splitTime > originalShot.startTime && splitTime < originalShot.endTime) {
          // 创建第二个片段
          const newShot: Shot = {
            ...originalShot,
            id: Date.now().toString(),
            startTime: splitTime,
          };
          
          // 更新原片段的结束时间
          state.shots[shotIndex].endTime = splitTime;
          
          // 添加新片段
          state.shots.push(newShot);
          state.shots.sort((a, b) => a.startTime - b.startTime);
        }
      }
    },
    
    mergeShots: (state, action: PayloadAction<string[]>) => {
      const shotIds = action.payload;
      if (shotIds.length < 2) return;

      // 按时间排序要合并的镜头
      const shotsToMerge = state.shots
        .filter(shot => shotIds.includes(shot.id))
        .sort((a, b) => a.startTime - b.startTime);

      if (shotsToMerge.length < 2) return;

      // 检查镜头是否相邻
      for (let i = 0; i < shotsToMerge.length - 1; i++) {
        if (Math.abs(shotsToMerge[i].endTime - shotsToMerge[i + 1].startTime) > 0.1) {
          return; // 镜头不相邻，不能合并
        }
      }

      // 创建合并后的镜头
      const mergedShot: Shot = {
        id: Date.now().toString(),
        startTime: shotsToMerge[0].startTime,
        endTime: shotsToMerge[shotsToMerge.length - 1].endTime,
        tags: [...new Set(shotsToMerge.flatMap(shot => shot.tags))],
        description: shotsToMerge.map(shot => shot.description).filter(Boolean).join(' | '),
        characters: [...new Set(shotsToMerge.flatMap(shot => shot.characters || []))],
      };

      // 移除原镜头并添加合并后的镜头
      state.shots = state.shots.filter(shot => !shotIds.includes(shot.id));
      state.shots.push(mergedShot);
      state.shots.sort((a, b) => a.startTime - b.startTime);
    },
  },
});

export const {
  addShot,
  updateShot,
  deleteShot,
  splitShot,
  mergeShots,
} = shotsSlice.actions;

export default shotsSlice.reducer;
