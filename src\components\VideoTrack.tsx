import React, { useState, useCallback, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../store/store';
import { setSelectedShotId } from '../store/slices/timelineSlice';
import { splitShot, updateShot, deleteShot, mergeShots } from '../store/slices/shotsSlice';
import { setRightPanelTab } from '../store/slices/uiSlice';
import ShotClip from './ShotClip';

interface VideoTrackProps {
  timeToPixel: (time: number) => number;
  pixelToTime: (pixel: number) => number;
  scrollLeft: number;
  activeTool: 'select' | 'blade' | 'merge';
}

const VideoTrack: React.FC<VideoTrackProps> = ({
  timeToPixel,
  pixelToTime,
  scrollLeft,
  activeTool,
}) => {
  const dispatch = useDispatch();
  const { shots } = useSelector((state: RootState) => state.shots);
  const { selectedShotId } = useSelector((state: RootState) => state.timeline);
  const { duration } = useSelector((state: RootState) => state.video);

  // 多选状态（用于合并工具）
  const [selectedShotIds, setSelectedShotIds] = useState<string[]>([]);

  // 同步多选状态到localStorage，供EditingToolbar使用
  useEffect(() => {
    localStorage.setItem('selectedShotIds', JSON.stringify(selectedShotIds));
    // 触发storage事件，通知EditingToolbar
    window.dispatchEvent(new StorageEvent('storage', {
      key: 'selectedShotIds',
      newValue: JSON.stringify(selectedShotIds)
    }));
  }, [selectedShotIds]);

  const [dragState, setDragState] = useState<{
    isDragging: boolean;
    dragType: 'move' | 'resize-start' | 'resize-end' | null;
    shotId: string | null;
    startX: number;
    originalStartTime: number;
    originalEndTime: number;
  }>({
    isDragging: false,
    dragType: null,
    shotId: null,
    startX: 0,
    originalStartTime: 0,
    originalEndTime: 0,
  });

  // 检查镜头是否相邻
  const areAdjacent = useCallback((shotIds: string[]) => {
    if (shotIds.length < 2) return true;

    const selectedShots = shots
      .filter(shot => shotIds.includes(shot.id))
      .sort((a, b) => a.startTime - b.startTime);

    for (let i = 0; i < selectedShots.length - 1; i++) {
      if (Math.abs(selectedShots[i].endTime - selectedShots[i + 1].startTime) > 0.1) {
        return false; // 镜头不相邻
      }
    }
    return true;
  }, [shots]);

  const handleShotClick = useCallback((e: React.MouseEvent, shotId: string) => {
    e.stopPropagation();

    if (activeTool === 'blade') {
      // 刀片工具：分割镜头
      const rect = e.currentTarget.getBoundingClientRect();
      const clickX = e.clientX - rect.left;
      const shot = shots.find(s => s.id === shotId);

      if (shot) {
        const shotStartPixel = timeToPixel(shot.startTime) - scrollLeft;
        const relativeClickX = clickX;
        const absoluteClickX = shotStartPixel + relativeClickX;
        const splitTime = pixelToTime(absoluteClickX + scrollLeft);

        if (splitTime > shot.startTime && splitTime < shot.endTime) {
          dispatch(splitShot({ shotId, splitTime }));
        }
      }
    } else if (activeTool === 'merge') {
      // 合并工具：智能多选镜头
      if (e.ctrlKey || e.metaKey) {
        // Ctrl/Cmd + 点击：添加/移除选择
        const newSelection = selectedShotIds.includes(shotId)
          ? selectedShotIds.filter(id => id !== shotId)
          : [...selectedShotIds, shotId];

        // 只有相邻镜头才能被选中
        if (areAdjacent(newSelection)) {
          setSelectedShotIds(newSelection);
        }
      } else if (e.shiftKey && selectedShotIds.length > 0) {
        // Shift + 点击：选择范围内的所有镜头
        const lastSelectedShot = shots.find(s => s.id === selectedShotIds[selectedShotIds.length - 1]);
        const currentShot = shots.find(s => s.id === shotId);

        if (lastSelectedShot && currentShot) {
          const startTime = Math.min(lastSelectedShot.startTime, currentShot.startTime);
          const endTime = Math.max(lastSelectedShot.endTime, currentShot.endTime);

          // 选择时间范围内的所有相邻镜头
          const rangeShots = shots
            .filter(shot => shot.startTime >= startTime && shot.endTime <= endTime)
            .sort((a, b) => a.startTime - b.startTime);

          const rangeIds = rangeShots.map(shot => shot.id);
          if (areAdjacent(rangeIds)) {
            setSelectedShotIds(rangeIds);
          }
        }
      } else {
        // 普通点击：重新选择
        setSelectedShotIds([shotId]);
      }
    } else {
      // 选择工具：选中镜头
      dispatch(setSelectedShotId(shotId));
      dispatch(setRightPanelTab('inspector'));
      setSelectedShotIds([]); // 清除多选
    }
  }, [activeTool, shots, timeToPixel, pixelToTime, scrollLeft, dispatch, selectedShotIds, areAdjacent]);

  const handleShotMouseDown = useCallback((e: React.MouseEvent, shotId: string, dragType: 'move' | 'resize-start' | 'resize-end') => {
    if (activeTool !== 'select') return;

    e.stopPropagation();
    e.preventDefault();

    const shot = shots.find(s => s.id === shotId);
    if (!shot) return;

    setDragState({
      isDragging: true,
      dragType,
      shotId,
      startX: e.clientX,
      originalStartTime: shot.startTime,
      originalEndTime: shot.endTime,
    });

    dispatch(setSelectedShotId(shotId));
  }, [activeTool, shots, dispatch]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!dragState.isDragging || !dragState.shotId) return;

    const deltaX = e.clientX - dragState.startX;
    const deltaTime = pixelToTime(deltaX);

    const shot = shots.find(s => s.id === dragState.shotId);
    if (!shot) return;

    let newStartTime = dragState.originalStartTime;
    let newEndTime = dragState.originalEndTime;

    switch (dragState.dragType) {
      case 'move':
        newStartTime = Math.max(0, dragState.originalStartTime + deltaTime);
        newEndTime = Math.min(duration, dragState.originalEndTime + deltaTime);
        
        // 保持镜头长度不变
        const shotDuration = dragState.originalEndTime - dragState.originalStartTime;
        if (newStartTime + shotDuration > duration) {
          newStartTime = duration - shotDuration;
          newEndTime = duration;
        }
        break;

      case 'resize-start':
        newStartTime = Math.max(0, Math.min(dragState.originalStartTime + deltaTime, dragState.originalEndTime - 0.1));
        break;

      case 'resize-end':
        newEndTime = Math.min(duration, Math.max(dragState.originalEndTime + deltaTime, dragState.originalStartTime + 0.1));
        break;
    }

    dispatch(updateShot({
      id: dragState.shotId,
      updates: { startTime: newStartTime, endTime: newEndTime }
    }));
  }, [dragState, shots, pixelToTime, duration, dispatch]);

  const handleMouseUp = useCallback(() => {
    setDragState({
      isDragging: false,
      dragType: null,
      shotId: null,
      startX: 0,
      originalStartTime: 0,
      originalEndTime: 0,
    });
  }, []);

  // 设置全局鼠标事件监听器
  React.useEffect(() => {
    if (dragState.isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [dragState.isDragging, handleMouseMove, handleMouseUp]);

  // 处理键盘删除和合并
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Delete') {
        if (selectedShotIds.length > 0) {
          // 删除多选的镜头
          selectedShotIds.forEach(id => dispatch(deleteShot(id)));
          setSelectedShotIds([]);
        } else if (selectedShotId) {
          // 删除单选的镜头
          dispatch(deleteShot(selectedShotId));
          dispatch(setSelectedShotId(null));
        }
      } else if (e.key === 'Enter' && selectedShotIds.length >= 2) {
        // 回车键合并选中的镜头
        dispatch(mergeShots(selectedShotIds));
        setSelectedShotIds([]);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedShotId, selectedShotIds, dispatch]);

  return (
    <div className="timeline-track h-32 relative">
      {/* 轨道标签 */}
      <div className="absolute left-2 top-2 text-xs text-gray-400 font-medium z-10">
        V1
      </div>

      {/* 镜头片段 */}
      {shots.map((shot) => (
        <ShotClip
          key={shot.id}
          shot={shot}
          isSelected={selectedShotId === shot.id || selectedShotIds.includes(shot.id)}
          timeToPixel={timeToPixel}
          scrollLeft={scrollLeft}
          onClick={(e) => handleShotClick(e, shot.id)}
          onMouseDown={(e, dragType) => handleShotMouseDown(e, shot.id, dragType)}
          activeTool={activeTool}
        />
      ))}

      {/* 拖拽时的视觉反馈 */}
      {dragState.isDragging && (
        <div className="absolute inset-0 bg-green-500 bg-opacity-10 pointer-events-none" />
      )}
    </div>
  );
};

export default VideoTrack;
