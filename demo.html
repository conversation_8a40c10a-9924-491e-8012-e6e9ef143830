<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频镜头分割与标注工具 - 演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #111827;
            color: white;
            overflow: hidden;
        }
        
        .app {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .toolbar {
            height: 48px;
            background: #1f2937;
            border-bottom: 1px solid #374151;
            display: flex;
            align-items: center;
            padding: 0 16px;
            gap: 16px;
        }
        
        .tool-group {
            display: flex;
            gap: 8px;
        }
        
        .tool-btn {
            padding: 8px;
            background: #374151;
            border: 1px solid #4b5563;
            border-radius: 6px;
            color: white;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .tool-btn:hover {
            background: #4b5563;
        }
        
        .tool-btn.active {
            background: #059669;
            border-color: #10b981;
        }
        
        .divider {
            width: 1px;
            height: 24px;
            background: #4b5563;
        }
        
        .title {
            flex: 1;
            text-align: center;
            font-size: 18px;
            font-weight: 600;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            overflow: hidden;
        }

        .resize-handle-vertical {
            width: 4px;
            background: #374151;
            cursor: col-resize;
            transition: background 0.2s;
        }

        .resize-handle-vertical:hover {
            background: #4b5563;
        }

        .resize-handle-horizontal {
            height: 4px;
            background: #374151;
            cursor: row-resize;
            transition: background 0.2s;
        }

        .resize-handle-horizontal:hover {
            background: #4b5563;
        }

        .video-list-panel {
            width: 280px;
            background: #1f2937;
            border-right: 1px solid #374151;
            display: flex;
            flex-direction: column;
        }

        .video-preview-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-width: 0;
        }
        
        .video-preview {
            flex: 1;
            padding: 4px;
        }
        
        .video-container {
            height: 100%;
            background: black;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        
        .video-placeholder {
            text-align: center;
            color: #6b7280;
        }
        
        .video-controls {
            height: 64px;
            background: #1f2937;
            border-top: 1px solid #374151;
            display: flex;
            align-items: center;
            padding: 0 16px;
            gap: 16px;
        }
        
        .play-btn {
            width: 40px;
            height: 40px;
            background: #059669;
            border: none;
            border-radius: 8px;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .time-display {
            font-size: 14px;
            color: #d1d5db;
        }
        
        .progress-bar {
            flex: 1;
            height: 8px;
            background: #374151;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: #059669;
            width: 30%;
            transition: width 0.1s;
        }
        
        .timeline {
            height: 324px;
            border-top: 1px solid #374151;
            background: #111827;
        }
        
        .timeline-ruler {
            height: 32px;
            background: #1f2937;
            border-bottom: 1px solid #374151;
            position: relative;
            overflow: hidden;
        }
        
        .timeline-tracks {
            flex: 1;
            position: relative;
        }
        
        .track {
            height: 128px;
            background: #1f2937;
            border-bottom: 1px solid #374151;
            position: relative;
            margin: 8px;
            border-radius: 8px;
        }
        
        .track-label {
            position: absolute;
            left: 8px;
            top: 8px;
            font-size: 12px;
            color: #9ca3af;
            font-weight: 500;
        }
        
        .shot-clip {
            position: absolute;
            top: 8px;
            height: 112px;
            background: linear-gradient(to right, #059669, #10b981);
            border: 1px solid #34d399;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .shot-clip:hover {
            background: linear-gradient(to right, #10b981, #34d399);
            border-color: #6ee7b7;
            box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
        }

        .shot-clip.selected {
            background: linear-gradient(to right, #34d399, #6ee7b7);
            border-color: #a7f3d0;
            box-shadow: 0 0 0 2px #6ee7b7;
        }
        
        .playhead {
            position: absolute;
            top: 0;
            width: 2px;
            height: 100%;
            background: #ef4444;
            z-index: 50;
            pointer-events: none;
        }
        
        .right-panel {
            width: 320px;
            background: #1f2937;
            border-left: 1px solid #374151;
            display: flex;
            flex-direction: column;
        }
        
        .panel-tabs {
            display: flex;
            border-bottom: 1px solid #374151;
        }
        
        .panel-tab {
            flex: 1;
            padding: 12px;
            background: none;
            border: none;
            color: #9ca3af;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .panel-tab.active {
            background: #374151;
            color: white;
            border-bottom: 2px solid #059669;
        }
        
        .panel-content {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
        }
        
        .shot-item {
            background: #374151;
            border: 1px solid #4b5563;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .shot-item:hover {
            background: #4b5563;
            border-color: #6b7280;
        }
        
        .shot-item.selected {
            background: #059669;
            border-color: #10b981;
        }
        
        .shot-thumbnail {
            width: 100%;
            height: 64px;
            background: #4b5563;
            border-radius: 4px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #9ca3af;
            font-size: 12px;
        }
        
        .demo-notice {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #059669;
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 1000;
            animation: slideIn 0.3s ease-out;
        }
        
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        .inspector-form {
            space-y: 16px;
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: white;
            margin-bottom: 8px;
        }
        
        .form-input {
            width: 100%;
            padding: 8px 12px;
            background: #374151;
            border: 1px solid #4b5563;
            border-radius: 6px;
            color: white;
            font-size: 14px;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #059669;
        }
        
        .tag {
            display: inline-block;
            background: #059669;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin: 2px;
        }

        /* 自定义音量滑块样式 */
        .volume-slider {
            -webkit-appearance: none;
            appearance: none;
            background: #374151;
            cursor: pointer;
            height: 8px;
            border-radius: 4px;
        }

        .volume-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            background: #10b981;
            height: 16px;
            width: 16px;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            transition: all 0.2s ease;
        }

        .volume-slider::-webkit-slider-thumb:hover {
            background: #34d399;
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <div class="demo-notice">
        🎬 这是一个静态演示页面，完整功能请运行开发服务器
    </div>
    
    <div class="app">
        <!-- 工具栏 -->
        <div class="toolbar">
            <div class="tool-group">
                <button class="tool-btn active" title="选择工具 (V)">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M3 3l7.07 16.97 2.51-7.39 7.39-2.51L3 3z"/>
                    </svg>
                </button>
                <button class="tool-btn" title="刀片工具 (C)">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="6" cy="6" r="3"/>
                        <circle cx="6" cy="18" r="3"/>
                        <line x1="20" y1="4" x2="8.12" y2="15.88"/>
                        <line x1="14.47" y1="14.48" x2="20" y2="20"/>
                        <line x1="8.12" y1="8.12" x2="12" y2="12"/>
                    </svg>
                </button>
            </div>
            
            <div class="divider"></div>
            
            <div class="tool-group">
                <button class="tool-btn" title="缩小">-</button>
                <span style="color: #9ca3af; font-size: 14px; min-width: 60px; text-align: center;">100%</span>
                <button class="tool-btn" title="放大">+</button>
                <button class="tool-btn" title="重置缩放">⟲</button>
            </div>
            
            <div class="title">视频镜头分割与标注工具</div>
        </div>
        
        <!-- 主内容区域 - 三列布局 -->
        <div class="main-content">
            <!-- 左侧：视频列表 -->
            <div class="video-list-panel">
                <div style="padding: 16px; border-bottom: 1px solid #374151;">
                    <h3 style="margin-bottom: 8px;">视频列表</h3>
                    <p style="color: #9ca3af; font-size: 14px;">共 3 个视频</p>
                </div>

                <div style="flex: 1; overflow-y: auto; padding: 16px;">
                    <!-- 当前选中的视频 -->
                    <div style="background: #059669; border: 1px solid #10b981; border-radius: 8px; padding: 12px; margin-bottom: 12px; cursor: pointer;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polygon points="23,7 16,12 23,17"/>
                                    <rect x="1" y="5" width="15" height="14" rx="2" ry="2"/>
                                </svg>
                                <span style="font-weight: 500; color: white;">本地视频</span>
                            </div>
                            <span style="font-size: 12px; color: #d1d5db;">5:00</span>
                        </div>
                        <div style="width: 100%; height: 64px; background: #374151; border-radius: 4px; margin-bottom: 8px; display: flex; align-items: center; justify-content: center;">
                            <span style="font-size: 12px; color: #9ca3af;">视频缩略图</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="font-size: 12px; color: #d1d5db;">本地视频 • public/1.mp4</span>
                            <button style="padding: 4px; background: none; border: none; color: white; cursor: pointer;">
                                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polygon points="5,3 19,12 5,21"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 添加视频按钮 -->
                <div style="padding: 16px; border-top: 1px solid #374151;">
                    <button style="width: 100%; padding: 8px; background: #059669; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">
                        + 添加视频
                    </button>
                </div>
            </div>

            <!-- 左侧垂直分割线 -->
            <div class="resize-handle-vertical"></div>

            <!-- 中间：视频预览 -->
            <div class="video-preview-panel" style="min-width: 400px;">
                <div class="video-preview">
                    <div class="video-container" style="height: 100%;">
                        <!-- 实际视频播放器 - 完全填充容器 -->
                        <video
                            id="mainVideo"
                            src="./public/1.mp4"
                            style="width: 100%; height: 100%; object-fit: contain; display: block;"
                            crossorigin="anonymous"
                            preload="metadata"
                        ></video>
                    </div>
                </div>

                <!-- 字幕显示区域 -->
                <div id="subtitleDisplay" style="height: 56px; background: #111827; border-top: 1px solid #374151; display: flex; align-items: center; justify-content: center; padding: 0 16px;">
                    <div style="text-align: center;">
                        <div id="subtitleText" style="color: white; font-size: 18px; font-weight: 500; line-height: 1.2; margin-bottom: 4px;">
                            欢迎来到视频编辑器
                        </div>
                        <div id="subtitleTime" style="color: #9ca3af; font-size: 12px;">
                            00:00 - 00:03
                        </div>
                    </div>
                </div>

                <!-- 视频控制栏 - 始终可见 -->
                <div class="video-controls" style="flex-shrink: 0;">
                    <button class="play-btn" onclick="togglePlay()">
                        <svg id="playIcon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polygon points="5,3 19,12 5,21"/>
                        </svg>
                        <svg id="pauseIcon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="display: none;">
                            <rect x="6" y="4" width="4" height="16"/>
                            <rect x="14" y="4" width="4" height="16"/>
                        </svg>
                    </button>

                    <div class="time-display" id="timeDisplay">00:00 / 05:00</div>

                    <div class="progress-bar" onclick="seekVideo(event)" style="position: relative; cursor: pointer;">
                        <div class="progress-fill" id="progressFill"></div>
                        <!-- 拖拽指示器 -->
                        <div id="progressThumb" style="position: absolute; top: 50%; transform: translateY(-50%); width: 12px; height: 12px; background: #10b981; border-radius: 50%; margin-left: -6px; left: 0%; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>
                    </div>

                    <button id="fitModeBtn" onclick="toggleFitMode()" style="padding: 4px 8px; font-size: 12px; background: #374151; color: #d1d5db; border: none; border-radius: 4px; cursor: pointer; margin-right: 8px;" title="切换视频适配模式">
                        适应
                    </button>

                    <button id="subtitleBtn" onclick="toggleSubtitles()" style="padding: 8px; background: #059669; color: white; border: none; border-radius: 4px; cursor: pointer; margin-right: 8px; display: flex; align-items: center;" title="显示/隐藏字幕">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M7 13h4v-2H7v2zm6-4H7V7h6v2zm4 4h-2v-2h2v2zm0-4h-2V7h2v2z"/>
                            <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
                        </svg>
                    </button>

                    <div style="display: flex; align-items: center; gap: 8px;">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"/>
                            <path d="M15.54 8.46a5 5 0 0 1 0 7.07"/>
                        </svg>
                        <input type="range" min="0" max="1" step="0.01" value="0.8" style="width: 80px;" onchange="setVolume(this.value)" class="volume-slider">
                    </div>
                </div>
            </div>

            <!-- 右侧垂直分割线 -->
            <div class="resize-handle-vertical"></div>

            <!-- 右侧：镜头面板 -->
            <div class="right-panel">
                <div class="panel-tabs">
                    <button class="panel-tab active">镜头列表</button>
                    <button class="panel-tab">属性面板</button>
                </div>

                <div class="panel-content">
                    <!-- 镜头列表 -->
                    <div>
                        <div class="shot-item">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <span style="font-weight: 500;">镜头 #1</span>
                                <span style="font-size: 12px; color: #9ca3af;">00:00 - 00:45</span>
                            </div>
                            <div style="margin-bottom: 8px;">
                                <span class="tag">开场</span>
                                <span class="tag">介绍</span>
                            </div>
                            <p style="font-size: 12px; color: #d1d5db;">视频开场介绍场景</p>
                        </div>

                        <div class="shot-item selected">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <span style="font-weight: 500;">镜头 #2</span>
                                <span style="font-size: 12px; color: #d1d5db;">00:45 - 02:00</span>
                            </div>
                            <div style="margin-bottom: 8px;">
                                <span class="tag">对话</span>
                                <span class="tag">室内</span>
                            </div>
                            <p style="font-size: 12px; color: #d1d5db;">主角与配角的对话场景</p>
                        </div>

                        <div class="shot-item">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <span style="font-weight: 500;">镜头 #3</span>
                                <span style="font-size: 12px; color: #9ca3af;">02:00 - 03:20</span>
                            </div>
                            <div style="margin-bottom: 8px;">
                                <span class="tag">动作</span>
                                <span class="tag">户外</span>
                            </div>
                            <p style="font-size: 12px; color: #d1d5db;">户外追逐场景</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 编辑工具栏 -->
        <div style="height: 48px; background: #1f2937; border-top: 1px solid #374151; border-bottom: 1px solid #374151; display: flex; align-items: center; padding: 0 16px; gap: 8px;">
            <span style="color: #9ca3af; font-size: 14px; margin-right: 16px;">编辑工具:</span>

            <button class="tool-btn active" title="选择工具 (V)">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="m3 3 7.07 16.97 2.51-7.39 7.39-2.51L3 3z"/>
                    <path d="m13 13 6 6"/>
                </svg>
                <span style="margin-left: 4px; font-size: 12px;">V</span>
            </button>

            <button class="tool-btn" title="切割工具 (C)">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="6" cy="6" r="3"/>
                    <circle cx="6" cy="18" r="3"/>
                    <line x1="20" y1="4" x2="8.12" y2="15.88"/>
                    <line x1="14.47" y1="14.48" x2="20" y2="20"/>
                    <line x1="8.12" y1="8.12" x2="12" y2="12"/>
                </svg>
                <span style="margin-left: 4px; font-size: 12px;">C</span>
            </button>

            <button class="tool-btn" title="合并工具 (M)">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M8 3v3a2 2 0 0 1-2 2H3"/>
                    <path d="M21 8h-3a2 2 0 0 1-2-2V3"/>
                    <path d="M3 16h3a2 2 0 0 1 2 2v3"/>
                    <path d="M16 21v-3a2 2 0 0 1 2-2h3"/>
                </svg>
                <span style="margin-left: 4px; font-size: 12px;">M</span>
            </button>

            <div style="margin-left: auto; font-size: 12px; color: #9ca3af;">
                提示: 选择相邻镜头后使用合并工具
            </div>
        </div>

        <!-- 水平分割线 -->
        <div class="resize-handle-horizontal"></div>

        <!-- 底部时间轴 - 全宽 -->
        <div class="timeline">
            <div class="timeline-ruler">
                <!-- 时间标尺内容 -->
            </div>

            <div class="timeline-tracks">
                <!-- 播放头 -->
                <div class="playhead" style="left: 30%;"></div>

                <!-- 视频轨道 -->
                <div class="track">
                    <div class="track-label">V1</div>
                    <!-- 镜头片段 -->
                    <div class="shot-clip" style="left: 40px; width: 180px;"></div>
                    <div class="shot-clip selected" style="left: 240px; width: 240px;"></div>
                    <div class="shot-clip" style="left: 500px; width: 160px;"></div>
                </div>

                <!-- 音频轨道 -->
                <div class="track">
                    <div class="track-label">A1</div>
                    <!-- 音频波形占位符 -->
                    <div style="position: absolute; left: 40px; right: 40px; top: 50%; height: 2px; background: #10b981; opacity: 0.6;"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 获取视频元素和控制元素
        const video = document.getElementById('mainVideo');
        const playIcon = document.getElementById('playIcon');
        const pauseIcon = document.getElementById('pauseIcon');
        const timeDisplay = document.getElementById('timeDisplay');
        const progressFill = document.getElementById('progressFill');
        const progressThumb = document.getElementById('progressThumb');
        const fitModeBtn = document.getElementById('fitModeBtn');
        const subtitleDisplay = document.getElementById('subtitleDisplay');
        const subtitleText = document.getElementById('subtitleText');
        const subtitleTime = document.getElementById('subtitleTime');
        const subtitleBtn = document.getElementById('subtitleBtn');

        // 视频适配模式状态
        let videoFitMode = 'contain';

        // 字幕状态
        let showSubtitles = true;

        // 示例字幕数据
        const subtitles = [
            { id: '1', startTime: 0, endTime: 3, text: '欢迎来到视频编辑器' },
            { id: '2', startTime: 3, endTime: 6, text: '这是一个功能强大的视频剪辑工具' },
            { id: '3', startTime: 6, endTime: 10, text: '您可以在这里进行视频编辑和字幕管理' },
            { id: '4', startTime: 15, endTime: 18, text: '支持多种视频格式和字幕功能' },
            { id: '5', startTime: 25, endTime: 30, text: '让我们开始您的创作之旅吧！' },
        ];

        // 格式化时间显示
        function formatTime(seconds) {
            const minutes = Math.floor(seconds / 60);
            const secs = Math.floor(seconds % 60);
            return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }

        // 播放/暂停切换
        function togglePlay() {
            if (video.paused) {
                video.play();
                playIcon.style.display = 'none';
                pauseIcon.style.display = 'block';
            } else {
                video.pause();
                playIcon.style.display = 'block';
                pauseIcon.style.display = 'none';
            }
        }

        // 设置音量
        function setVolume(value) {
            video.volume = parseFloat(value);
        }

        // 切换视频适配模式
        function toggleFitMode() {
            videoFitMode = videoFitMode === 'contain' ? 'cover' : 'contain';
            video.style.objectFit = videoFitMode;
            fitModeBtn.textContent = videoFitMode === 'contain' ? '适应' : '填充';
            fitModeBtn.title = `当前: ${videoFitMode === 'contain' ? '适应' : '填充'} - 点击切换`;
        }

        // 切换字幕显示
        function toggleSubtitles() {
            showSubtitles = !showSubtitles;
            updateSubtitleDisplay();

            // 更新按钮样式
            if (showSubtitles) {
                subtitleBtn.style.background = '#059669';
                subtitleBtn.style.color = 'white';
                subtitleBtn.title = '隐藏字幕';
            } else {
                subtitleBtn.style.background = '#374151';
                subtitleBtn.style.color = '#9ca3af';
                subtitleBtn.title = '显示字幕';
            }
        }

        // 更新字幕显示
        function updateSubtitleDisplay() {
            if (!showSubtitles) {
                subtitleText.textContent = '字幕已隐藏';
                subtitleTime.textContent = '';
                subtitleText.style.color = '#6b7280';
                return;
            }

            const currentTime = video.currentTime;
            const currentSubtitle = subtitles.find(
                subtitle => currentTime >= subtitle.startTime && currentTime <= subtitle.endTime
            );

            if (currentSubtitle) {
                subtitleText.textContent = currentSubtitle.text;
                subtitleTime.textContent = `${formatTime(currentSubtitle.startTime)} - ${formatTime(currentSubtitle.endTime)}`;
                subtitleText.style.color = 'white';
            } else {
                subtitleText.textContent = '暂无字幕';
                subtitleTime.textContent = '';
                subtitleText.style.color = '#6b7280';
            }
        }

        // 进度条点击跳转
        function seekVideo(event) {
            const progressBar = event.currentTarget;
            const rect = progressBar.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const percentage = x / rect.width;
            const newTime = percentage * video.duration;
            video.currentTime = newTime;
        }

        // 更新进度条和时间显示
        function updateProgress() {
            if (video.duration) {
                const percentage = (video.currentTime / video.duration) * 100;
                progressFill.style.width = percentage + '%';
                progressThumb.style.left = percentage + '%';
                timeDisplay.textContent = `${formatTime(video.currentTime)} / ${formatTime(video.duration)}`;

                // 更新字幕显示
                updateSubtitleDisplay();
            }
        }

        // 视频事件监听
        video.addEventListener('timeupdate', updateProgress);
        video.addEventListener('loadedmetadata', () => {
            timeDisplay.textContent = `00:00 / ${formatTime(video.duration)}`;
        });
        video.addEventListener('ended', () => {
            playIcon.style.display = 'block';
            pauseIcon.style.display = 'none';
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.target.tagName === 'INPUT') return;

            switch(e.key) {
                case ' ':
                    e.preventDefault();
                    togglePlay();
                    break;
                case 'ArrowLeft':
                    video.currentTime = Math.max(0, video.currentTime - 5);
                    break;
                case 'ArrowRight':
                    video.currentTime = Math.min(video.duration, video.currentTime + 5);
                    break;
            }
        });

        // 初始化音量
        video.volume = 0.8;
    </script>
</body>
</html>
