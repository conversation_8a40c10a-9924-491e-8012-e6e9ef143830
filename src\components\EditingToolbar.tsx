import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { MousePointer, Scissors, Merge } from 'lucide-react';
import { RootState } from '../store/store';
import { setActiveTool } from '../store/slices/uiSlice';
import { mergeShots } from '../store/slices/shotsSlice';

const EditingToolbar: React.FC = () => {
  const dispatch = useDispatch();
  const { activeTool } = useSelector((state: RootState) => state.ui);
  const [selectedShotIds, setSelectedShotIds] = useState<string[]>([]);

  // 监听全局选择状态
  useEffect(() => {
    // 初始化时读取localStorage中的值
    const stored = localStorage.getItem('selectedShotIds');
    if (stored) {
      setSelectedShotIds(JSON.parse(stored));
    }

    // 监听自定义事件
    const handleShotSelectionChange = (e: CustomEvent) => {
      setSelectedShotIds(e.detail);
    };

    window.addEventListener('shotSelectionChange', handleShotSelectionChange as EventListener);
    return () => window.removeEventListener('shotSelectionChange', handleShotSelectionChange as EventListener);
  }, []);

  const handleMergeClick = () => {
    if (selectedShotIds.length >= 2) {
      dispatch(mergeShots(selectedShotIds));
      setSelectedShotIds([]);
      localStorage.setItem('selectedShotIds', '[]');
      // 触发事件通知其他组件
      window.dispatchEvent(new CustomEvent('shotSelectionChange', {
        detail: []
      }));
    }
  };

  const tools = [
    {
      id: 'select' as const,
      icon: MousePointer,
      label: '选择工具',
      shortcut: 'V',
    },
    {
      id: 'blade' as const,
      icon: Scissors,
      label: '切割工具',
      shortcut: 'C',
    },
    {
      id: 'merge' as const,
      icon: Merge,
      label: '合并工具',
      shortcut: 'M',
    },
  ];

  return (
    <div className="h-12 bg-gray-800 border-y border-gray-700 flex items-center px-4 gap-3">
      <span className="text-sm text-gray-400 mr-2">编辑工具:</span>

      {tools.map((tool) => {
        const Icon = tool.icon;
        return (
          <button
            key={tool.id}
            onClick={() => {
              dispatch(setActiveTool(tool.id));
            }}
            className={`flex flex-col items-center justify-center w-12 h-10 rounded-lg transition-colors duration-200 border ${
              activeTool === tool.id
                ? 'bg-green-600 hover:bg-green-500 border-green-500 text-white'
                : 'bg-gray-700 hover:bg-gray-600 border-gray-600 text-gray-300'
            } ${
              tool.id === 'merge' && selectedShotIds.length < 2 ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            title={`${tool.label} (${tool.shortcut})`}
            disabled={tool.id === 'merge' && selectedShotIds.length < 2}
          >
            <Icon size={14} />
            <span className="text-xs mt-0.5 leading-none">{tool.shortcut}</span>
          </button>
        );
      })}

      {/* 合并执行按钮 */}
      {activeTool === 'merge' && selectedShotIds.length >= 2 && (
        <button
          onClick={handleMergeClick}
          className="px-3 py-1 bg-green-600 hover:bg-green-500 text-white text-xs rounded transition-colors"
        >
          执行合并
        </button>
      )}

      <div className="ml-auto text-xs text-gray-400">
        {activeTool === 'merge'
          ? selectedShotIds.length >= 2
            ? `已选择 ${selectedShotIds.length} 个镜头 (点击"执行合并"或按Enter键)`
            : `已选择 ${selectedShotIds.length} 个镜头 (需要至少2个相邻镜头)`
          : '提示: 切换到合并工具后选择相邻镜头'
        }
      </div>
    </div>
  );
};

export default EditingToolbar;
