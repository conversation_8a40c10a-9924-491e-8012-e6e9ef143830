import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { MousePointer, Scissors, Merge } from 'lucide-react';
import { RootState } from '../store/store';
import { setActiveTool } from '../store/slices/uiSlice';
import { mergeShots } from '../store/slices/shotsSlice';

const EditingToolbar: React.FC = () => {
  const dispatch = useDispatch();
  const { activeTool } = useSelector((state: RootState) => state.ui);
  const [selectedShotIds, setSelectedShotIds] = useState<string[]>([]);

  // 监听全局选择状态（这里简化处理，实际应该通过Redux管理）
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'selectedShotIds') {
        setSelectedShotIds(JSON.parse(e.newValue || '[]'));
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  const handleMergeClick = () => {
    if (selectedShotIds.length >= 2) {
      dispatch(mergeShots(selectedShotIds));
      setSelectedShotIds([]);
      localStorage.setItem('selectedShotIds', '[]');
    }
  };

  const tools = [
    {
      id: 'select' as const,
      icon: MousePointer,
      label: '选择工具',
      shortcut: 'V',
    },
    {
      id: 'blade' as const,
      icon: Scissors,
      label: '切割工具',
      shortcut: 'C',
    },
    {
      id: 'merge' as const,
      icon: Merge,
      label: '合并工具',
      shortcut: 'M',
    },
  ];

  return (
    <div className="h-12 bg-gray-800 border-y border-gray-700 flex items-center px-4 gap-2">
      <span className="text-sm text-gray-400 mr-4">编辑工具:</span>
      
      {tools.map((tool) => {
        const Icon = tool.icon;
        return (
          <button
            key={tool.id}
            onClick={() => {
              if (tool.id === 'merge' && selectedShotIds.length >= 2) {
                handleMergeClick();
              } else {
                dispatch(setActiveTool(tool.id));
              }
            }}
            className={`tool-button ${activeTool === tool.id ? 'active' : ''} ${
              tool.id === 'merge' && selectedShotIds.length < 2 ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            title={`${tool.label} (${tool.shortcut})`}
            disabled={tool.id === 'merge' && selectedShotIds.length < 2}
          >
            <Icon size={16} />
            <span className="ml-1 text-xs">{tool.shortcut}</span>
          </button>
        );
      })}
      
      <div className="ml-auto text-xs text-gray-400">
        {activeTool === 'merge'
          ? `已选择 ${selectedShotIds.length} 个镜头 ${selectedShotIds.length >= 2 ? '(可合并)' : '(需要至少2个)'}`
          : '提示: 选择相邻镜头后使用合并工具'
        }
      </div>
    </div>
  );
};

export default EditingToolbar;
