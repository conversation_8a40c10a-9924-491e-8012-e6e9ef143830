import React, { useRef, useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Play, Pause, Volume2, Subtitles } from 'lucide-react';
import { RootState } from '../store/store';
import { setCurrentTime, setIsPlaying, togglePlayPause, setDuration, setVolume, setVideoUrl, toggleSubtitles } from '../store/slices/videoSlice';
import { setPlayheadPosition } from '../store/slices/timelineSlice';
import SubtitleDisplay from './SubtitleDisplay';

const VideoPreview: React.FC = () => {
  const dispatch = useDispatch();
  const videoRef = useRef<HTMLVideoElement>(null);
  const progressRef = useRef<HTMLDivElement>(null);
  const volumeRef = useRef<HTMLInputElement>(null);
  const [isDraggingProgress, setIsDraggingProgress] = useState(false);
  const [isDraggingVolume, setIsDraggingVolume] = useState(false);
  const [videoFit, setVideoFit] = useState<'contain' | 'cover'>('contain');
  const { url, currentTime, isPlaying, volume, duration, showSubtitles } = useSelector((state: RootState) => state.video);

  // 同步视频播放状态
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    if (isPlaying) {
      video.play();
    } else {
      video.pause();
    }
  }, [isPlaying]);

  // 同步当前时间
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    if (Math.abs(video.currentTime - currentTime) > 0.1) {
      video.currentTime = currentTime;
    }
  }, [currentTime]);

  // 同步音量
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    video.volume = volume;
  }, [volume]);

  const handleTimeUpdate = () => {
    const video = videoRef.current;
    if (!video) return;

    const time = video.currentTime;
    dispatch(setCurrentTime(time));
    dispatch(setPlayheadPosition(time));
  };

  const handleLoadedMetadata = () => {
    const video = videoRef.current;
    if (!video) return;

    dispatch(setDuration(video.duration));
  };

  const handlePlayPause = () => {
    dispatch(togglePlayPause());
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // 进度条拖拽处理
  const handleProgressMouseDown = (e: React.MouseEvent) => {
    setIsDraggingProgress(true);
    handleProgressUpdate(e);
  };

  const handleProgressUpdate = (e: React.MouseEvent | MouseEvent) => {
    if (!progressRef.current || duration === 0) return;

    const rect = progressRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const percentage = Math.max(0, Math.min(1, x / rect.width));
    const newTime = percentage * duration;

    dispatch(setCurrentTime(newTime));
    dispatch(setPlayheadPosition(newTime));
  };

  // 音量拖拽处理
  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value);
    dispatch(setVolume(newVolume));
  };

  // 全局鼠标事件处理
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (isDraggingProgress) {
        handleProgressUpdate(e);
      }
    };

    const handleMouseUp = () => {
      setIsDraggingProgress(false);
      setIsDraggingVolume(false);
    };

    if (isDraggingProgress || isDraggingVolume) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDraggingProgress, isDraggingVolume, duration]);



  return (
    <div className="h-full flex flex-col bg-black rounded-lg overflow-hidden">
      {/* 视频容器 - 完全填充，无缝隙 */}
      <div className="flex-1 relative bg-black">
        {url ? (
          /* 真实视频播放器 - 完全填充容器 */
          <video
            ref={videoRef}
            src={url}
            className={`w-full h-full ${videoFit === 'contain' ? 'object-contain' : 'object-cover'}`}
            style={{ display: 'block' }}
            onTimeUpdate={handleTimeUpdate}
            onLoadedMetadata={handleLoadedMetadata}
            onEnded={() => dispatch(setIsPlaying(false))}
            crossOrigin="anonymous"
          />
        ) : (
          /* 视频播放器占位符 */
          <div className="w-full h-full flex items-center justify-center bg-gray-800">
            <div className="text-center">
              <div className="w-32 h-32 bg-gray-700 rounded-lg mx-auto mb-4 flex items-center justify-center">
                <Play size={48} className="text-gray-500" />
              </div>
              <p className="text-gray-400">视频预览区域</p>
              <p className="text-sm text-gray-500 mt-2">
                分辨率: 640x324 • 时长: {formatTime(duration)}
              </p>
              <div className="mt-4">
                <p className="text-xs text-gray-500">
                  请将视频文件 1.mp4 放置在 public/ 目录中
                </p>
                <button
                  onClick={() => dispatch(setVideoUrl('/1.mp4'))}
                  className="mt-2 px-4 py-2 bg-green-600 hover:bg-green-500 text-white rounded text-sm transition-colors"
                >
                  加载本地视频
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 字幕显示区域 */}
      <SubtitleDisplay />

      {/* 控制栏 - 始终可见 */}
      <div className="h-16 bg-gray-800 border-t border-gray-700 flex items-center px-4 gap-4 flex-shrink-0">
        {/* 播放/暂停按钮 */}
        <button
          onClick={handlePlayPause}
          className="p-2 rounded-lg bg-green-600 hover:bg-green-500 transition-colors flex-shrink-0"
        >
          {isPlaying ? <Pause size={20} /> : <Play size={20} />}
        </button>

        {/* 时间显示 */}
        <div className="text-sm text-gray-300 flex-shrink-0 min-w-[100px]">
          {formatTime(currentTime)} / {formatTime(duration)}
        </div>

        {/* 可拖拽进度条 */}
        <div
          ref={progressRef}
          className="flex-1 h-2 bg-gray-700 rounded-full overflow-hidden cursor-pointer relative"
          onMouseDown={handleProgressMouseDown}
        >
          <div
            className="h-full bg-green-500 transition-all duration-100 pointer-events-none"
            style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
          />
          {/* 拖拽指示器 */}
          <div
            className="absolute top-1/2 transform -translate-y-1/2 w-3 h-3 bg-green-400 rounded-full shadow-lg pointer-events-none"
            style={{ left: `${duration > 0 ? (currentTime / duration) * 100 : 0}%`, marginLeft: '-6px' }}
          />
        </div>

        {/* 视频适配模式切换 */}
        <button
          onClick={() => setVideoFit(videoFit === 'contain' ? 'cover' : 'contain')}
          className="px-2 py-1 text-xs bg-gray-700 hover:bg-gray-600 text-gray-300 rounded transition-colors flex-shrink-0"
          title={`当前: ${videoFit === 'contain' ? '适应' : '填充'} - 点击切换`}
        >
          {videoFit === 'contain' ? '适应' : '填充'}
        </button>

        {/* 字幕开关 */}
        <button
          onClick={() => dispatch(toggleSubtitles())}
          className={`p-2 rounded transition-colors flex-shrink-0 ${
            showSubtitles
              ? 'bg-green-600 hover:bg-green-500 text-white'
              : 'bg-gray-700 hover:bg-gray-600 text-gray-400'
          }`}
          title={showSubtitles ? '隐藏字幕' : '显示字幕'}
        >
          <Subtitles size={16} />
        </button>

        {/* 音量控制 */}
        <div className="flex items-center gap-2 flex-shrink-0">
          <Volume2 size={16} className="text-gray-400" />
          <input
            ref={volumeRef}
            type="range"
            min="0"
            max="1"
            step="0.01"
            value={volume}
            onChange={handleVolumeChange}
            className="w-20 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-green"
          />
        </div>
      </div>
    </div>
  );
};

export default VideoPreview;
