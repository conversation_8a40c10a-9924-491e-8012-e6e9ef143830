import React from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../store/store';

const SubtitleDisplay: React.FC = () => {
  const { currentTime, subtitles, showSubtitles } = useSelector((state: RootState) => state.video);

  // 查找当前时间对应的字幕
  const currentSubtitle = subtitles.find(
    subtitle => currentTime >= subtitle.startTime && currentTime <= subtitle.endTime
  );

  if (!showSubtitles || !currentSubtitle) {
    return (
      <div className="h-14 bg-gray-900 border-t border-gray-700 flex items-center justify-center">
        <div className="text-gray-500 text-sm">
          {showSubtitles ? '暂无字幕' : '字幕已隐藏'}
        </div>
      </div>
    );
  }

  return (
    <div className="h-14 bg-gray-900 border-t border-gray-700 flex items-center justify-center px-4">
      <div className="text-center">
        <div className="text-white text-lg font-medium leading-tight">
          {currentSubtitle.text}
        </div>
        <div className="text-gray-400 text-xs mt-1">
          {formatTime(currentSubtitle.startTime)} - {formatTime(currentSubtitle.endTime)}
        </div>
      </div>
    </div>
  );
};

// 时间格式化函数
const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

export default SubtitleDisplay;
