import React, { useEffect, useState, useRef, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import VideoPreview from './components/VideoPreview';
import Timeline from './components/Timeline';
import RightPanel from './components/RightPanel';
import VideoList from './components/VideoList';
import EditingToolbar from './components/EditingToolbar';
import { setActiveTool } from './store/slices/uiSlice';

function App() {
  const dispatch = useDispatch();
  const [leftPanelWidth, setLeftPanelWidth] = useState(280);
  const [rightPanelWidth, setRightPanelWidth] = useState(320);
  const [timelineHeight, setTimelineHeight] = useState(324);
  const isDraggingRef = useRef(false);
  const dragTypeRef = useRef<'left-vertical' | 'right-vertical' | 'horizontal' | null>(null);

  // 拖拽处理函数
  const handleMouseDown = useCallback((e: React.MouseEvent, type: 'left-vertical' | 'right-vertical' | 'horizontal') => {
    e.preventDefault();
    isDraggingRef.current = true;
    dragTypeRef.current = type;

    const handleMouseMove = (e: MouseEvent) => {
      if (!isDraggingRef.current) return;

      if (dragTypeRef.current === 'left-vertical') {
        // 左侧垂直拖拽调整左侧面板宽度
        const newWidth = e.clientX;
        // 视频列表面板最小宽度200px，最大宽度400px
        setLeftPanelWidth(Math.max(200, Math.min(400, newWidth)));
      } else if (dragTypeRef.current === 'right-vertical') {
        // 右侧垂直拖拽调整右侧面板宽度
        const newWidth = window.innerWidth - e.clientX;
        // 镜头面板最小宽度280px，最大宽度600px
        setRightPanelWidth(Math.max(280, Math.min(600, newWidth)));
      } else if (dragTypeRef.current === 'horizontal') {
        // 水平拖拽调整时间轴高度
        const newHeight = window.innerHeight - e.clientY;
        setTimelineHeight(Math.max(200, Math.min(500, newHeight)));
      }
    };

    const handleMouseUp = () => {
      isDraggingRef.current = false;
      dragTypeRef.current = null;
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, []);

  // 全局快捷键处理
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 防止在输入框中触发快捷键
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return;
      }

      switch (e.key.toLowerCase()) {
        case 'v':
          dispatch(setActiveTool('select'));
          break;
        case 'c':
          dispatch(setActiveTool('blade'));
          break;
        case 'm':
          dispatch(setActiveTool('merge'));
          break;
        case ' ':
          e.preventDefault();
          // 播放/暂停逻辑将在VideoPreview组件中处理
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [dispatch]);

  return (
    <div className="h-screen bg-gray-900 text-white flex flex-col overflow-hidden">
      {/* 主要内容区域 - 三列等高布局 */}
      <div className="flex-1 flex overflow-hidden" style={{ height: `calc(100vh - 48px - ${timelineHeight}px)` }}>
        {/* 左侧：视频列表 */}
        <div
          className="border-r border-gray-700 flex-shrink-0"
          style={{ width: `${leftPanelWidth}px` }}
        >
          <VideoList />
        </div>

        {/* 左侧垂直分割线 */}
        <div
          className="w-1 bg-gray-700 hover:bg-gray-600 cursor-col-resize transition-colors"
          onMouseDown={(e) => handleMouseDown(e, 'left-vertical')}
        />

        {/* 中间：视频预览 */}
        <div className="flex-1 flex flex-col" style={{ minWidth: '400px' }}>
          <div className="flex-1 p-1">
            <VideoPreview />
          </div>
        </div>

        {/* 右侧垂直分割线 */}
        <div
          className="w-1 bg-gray-700 hover:bg-gray-600 cursor-col-resize transition-colors"
          onMouseDown={(e) => handleMouseDown(e, 'right-vertical')}
        />

        {/* 右侧：镜头面板 */}
        <div
          className="border-l border-gray-700 flex-shrink-0"
          style={{ width: `${rightPanelWidth}px` }}
        >
          <RightPanel />
        </div>
      </div>

      {/* 编辑工具栏 */}
      <EditingToolbar />

      {/* 水平分割线 */}
      <div
        className="h-1 bg-gray-700 hover:bg-gray-600 cursor-row-resize transition-colors"
        onMouseDown={(e) => handleMouseDown(e, 'horizontal')}
      />

      {/* 底部：时间轴 - 全宽 */}
      <div
        className="border-t border-gray-700"
        style={{ height: `${timelineHeight}px` }}
      >
        <Timeline />
      </div>
    </div>
  );
}

export default App;
