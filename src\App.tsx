import React, { useEffect, useState, useRef, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import VideoPreview from './components/VideoPreview';
import Timeline from './components/Timeline';
import RightPanel from './components/RightPanel';
import VideoList from './components/VideoList';
import EditingToolbar from './components/EditingToolbar';
import { setActiveTool } from './store/slices/uiSlice';

function App() {
  const dispatch = useDispatch();
  // 使用百分比来管理宽度
  const [leftPanelWidthPercent, setLeftPanelWidthPercent] = useState(20); // 视频列表区域默认20%
  const [rightPanelWidthPercent, setRightPanelWidthPercent] = useState(30); // 镜头区域默认30%
  // 中间视频预览区域宽度 = 100% - 左侧% - 右侧%
  const centerPanelWidthPercent = 100 - leftPanelWidthPercent - rightPanelWidthPercent;

  const [timelineHeight, setTimelineHeight] = useState(324);
  const isDraggingRef = useRef(false);
  const dragTypeRef = useRef<'left-vertical' | 'right-vertical' | 'horizontal' | null>(null);

  // 计算中间视频预览区域的实际像素宽度（用于高度计算）
  const centerPanelWidth = (centerPanelWidthPercent / 100) * window.innerWidth;

  // 计算视频预览区域的高度（当中间区域占60%时）
  const calculateVideoPreviewHeight = () => {
    if (centerPanelWidthPercent === 60) {
      // 假设视频比例为16:9，加上字幕区域和控制栏
      const videoAspectRatio = 16 / 9;
      const videoWidth = centerPanelWidth;
      const videoHeight = videoWidth / videoAspectRatio;
      const subtitleHeight = 60; // 字幕区域高度
      const controlHeight = 64; // 控制栏高度
      return videoHeight + subtitleHeight + controlHeight;
    }
    return null;
  };

  // 拖拽处理函数
  const handleMouseDown = useCallback((e: React.MouseEvent, type: 'left-vertical' | 'right-vertical' | 'horizontal') => {
    e.preventDefault();
    isDraggingRef.current = true;
    dragTypeRef.current = type;

    const handleMouseMove = (e: MouseEvent) => {
      if (!isDraggingRef.current) return;
      const currentScreenWidth = window.innerWidth;

      if (dragTypeRef.current === 'left-vertical') {
        // 左侧垂直拖拽调整左侧面板宽度百分比
        const newWidthPercent = (e.clientX / currentScreenWidth) * 100;
        // 视频列表面板最小20%，最大50%
        const clampedPercent = Math.max(20, Math.min(50, newWidthPercent));
        setLeftPanelWidthPercent(clampedPercent);

        // 确保中间区域不小于30%，不大于60%
        const newCenterPercent = 100 - clampedPercent - rightPanelWidthPercent;
        if (newCenterPercent < 30) {
          setRightPanelWidthPercent(100 - clampedPercent - 30);
        } else if (newCenterPercent > 60) {
          setRightPanelWidthPercent(100 - clampedPercent - 60);
        }
      } else if (dragTypeRef.current === 'right-vertical') {
        // 右侧垂直拖拽调整右侧面板宽度百分比
        const newWidthPercent = ((currentScreenWidth - e.clientX) / currentScreenWidth) * 100;
        // 镜头面板最小20%，最大50%
        const clampedPercent = Math.max(20, Math.min(50, newWidthPercent));
        setRightPanelWidthPercent(clampedPercent);

        // 确保中间区域不小于30%，不大于60%
        const newCenterPercent = 100 - leftPanelWidthPercent - clampedPercent;
        if (newCenterPercent < 30) {
          setLeftPanelWidthPercent(100 - clampedPercent - 30);
        } else if (newCenterPercent > 60) {
          setLeftPanelWidthPercent(100 - clampedPercent - 60);
        }
      } else if (dragTypeRef.current === 'horizontal') {
        // 水平拖拽调整时间轴高度
        const videoPreviewHeight = calculateVideoPreviewHeight();
        if (videoPreviewHeight && centerPanelWidthPercent === 60) {
          // 当中间区域占60%时，时间轴高度 = 屏幕高度 - 视频预览高度
          const newTimelineHeight = window.innerHeight - videoPreviewHeight;
          setTimelineHeight(Math.max(200, newTimelineHeight));
        } else {
          // 其他情况下的正常拖拽
          const newHeight = window.innerHeight - e.clientY;
          setTimelineHeight(Math.max(200, Math.min(500, newHeight)));
        }
      }
    };

    const handleMouseUp = () => {
      isDraggingRef.current = false;
      dragTypeRef.current = null;
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [leftPanelWidthPercent, rightPanelWidthPercent, centerPanelWidthPercent]);

  // 窗口大小变化监听
  useEffect(() => {
    const handleResize = () => {
      // 强制重新渲染以更新宽度计算
      setLeftPanelWidthPercent(prev => prev);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 全局快捷键处理
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 防止在输入框中触发快捷键
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return;
      }

      switch (e.key.toLowerCase()) {
        case 'v':
          dispatch(setActiveTool('select'));
          break;
        case 'c':
          dispatch(setActiveTool('blade'));
          break;
        case 'm':
          dispatch(setActiveTool('merge'));
          break;
        case ' ':
          e.preventDefault();
          // 播放/暂停逻辑将在VideoPreview组件中处理
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [dispatch]);

  // 计算时间轴高度
  const calculateTimelineHeight = () => {
    const videoPreviewHeight = calculateVideoPreviewHeight();
    if (videoPreviewHeight && centerPanelWidthPercent === 60) {
      // 当中间区域占60%时，时间轴高度 = 屏幕高度 - 视频预览高度 - 工具栏高度
      return window.innerHeight - videoPreviewHeight - 48; // 48px是工具栏高度
    }
    return timelineHeight;
  };

  const finalTimelineHeight = calculateTimelineHeight();

  return (
    <div className="h-screen bg-gray-900 text-white flex flex-col overflow-hidden">
      {/* 主要内容区域 - 三列等高布局 */}
      <div className="flex-1 flex overflow-hidden" style={{ height: `calc(100vh - 48px - ${finalTimelineHeight}px)` }}>
        {/* 左侧：视频列表 */}
        <div
          className="border-r border-gray-700 flex-shrink-0"
          style={{ width: `${leftPanelWidthPercent}%` }}
        >
          <VideoList />
        </div>

        {/* 左侧垂直分割线 */}
        <div
          className="w-1 bg-gray-700 hover:bg-gray-600 cursor-col-resize transition-colors"
          onMouseDown={(e) => handleMouseDown(e, 'left-vertical')}
        />

        {/* 中间：视频预览 */}
        <div
          className="flex flex-col"
          style={{
            width: `${centerPanelWidthPercent}%`,
            minWidth: '30%',
            maxWidth: '60%'
          }}
        >
          <div className="flex-1 p-1">
            <VideoPreview />
          </div>
        </div>

        {/* 右侧垂直分割线 */}
        <div
          className="w-1 bg-gray-700 hover:bg-gray-600 cursor-col-resize transition-colors"
          onMouseDown={(e) => handleMouseDown(e, 'right-vertical')}
        />

        {/* 右侧：镜头面板 */}
        <div
          className="border-l border-gray-700 flex-shrink-0"
          style={{ width: `${rightPanelWidthPercent}%` }}
        >
          <RightPanel />
        </div>
      </div>

      {/* 编辑工具栏 */}
      <EditingToolbar />

      {/* 水平分割线 - 只在非60%模式下显示 */}
      {centerPanelWidthPercent !== 60 && (
        <div
          className="h-1 bg-gray-700 hover:bg-gray-600 cursor-row-resize transition-colors"
          onMouseDown={(e) => handleMouseDown(e, 'horizontal')}
        />
      )}

      {/* 底部：时间轴 - 全宽 */}
      <div
        className="border-t border-gray-700"
        style={{ height: `${finalTimelineHeight}px` }}
      >
        <Timeline />
      </div>
    </div>
  );
}

export default App;
