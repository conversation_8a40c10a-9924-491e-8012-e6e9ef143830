import React from 'react';

interface AudioTrackProps {
  timeToPixel: (time: number) => number;
  scrollLeft: number;
  duration: number;
}

const AudioTrack: React.FC<AudioTrackProps> = ({
  timeToPixel,
  scrollLeft,
  duration,
}) => {
  // 生成模拟音频波形数据
  const generateWaveform = () => {
    const width = timeToPixel(duration);
    const points = Math.floor(width / 2); // 每2像素一个点
    const waveform = [];

    for (let i = 0; i < points; i++) {
      // 生成随机波形数据，模拟音频
      const amplitude = Math.random() * 0.8 + 0.1; // 0.1 到 0.9 之间
      const frequency = Math.sin(i * 0.1) * 0.3 + 0.5; // 添加一些周期性变化
      waveform.push(amplitude * frequency);
    }

    return waveform;
  };

  const waveformData = generateWaveform();
  const trackHeight = 80;
  const waveformHeight = trackHeight - 16; // 留出上下边距

  return (
    <div className="timeline-track h-20 relative bg-gray-800 border-t border-gray-700">
      {/* 轨道标签 */}
      <div className="absolute left-2 top-2 text-xs text-gray-400 font-medium z-10">
        A1
      </div>

      {/* 音频波形 */}
      <div className="absolute inset-0 top-2 bottom-2 left-8 right-0 overflow-hidden">
        <svg
          width={timeToPixel(duration)}
          height={waveformHeight}
          className="block"
          style={{ transform: `translateX(-${scrollLeft}px)` }}
        >
          {/* 波形路径 */}
          <path
            d={waveformData
              .map((amplitude, index) => {
                const x = index * 2;
                const centerY = waveformHeight / 2;
                const y1 = centerY - (amplitude * waveformHeight * 0.4);
                const y2 = centerY + (amplitude * waveformHeight * 0.4);
                
                if (index === 0) {
                  return `M ${x} ${y1} L ${x} ${y2}`;
                }
                return `M ${x} ${y1} L ${x} ${y2}`;
              })
              .join(' ')}
            stroke="#10b981"
            strokeWidth="1"
            fill="none"
            opacity="0.8"
          />

          {/* 波形填充 */}
          <path
            d={`M 0 ${waveformHeight / 2} ${waveformData
              .map((amplitude, index) => {
                const x = index * 2;
                const centerY = waveformHeight / 2;
                const y = centerY - (amplitude * waveformHeight * 0.4);
                return `L ${x} ${y}`;
              })
              .join(' ')} L ${waveformData.length * 2} ${waveformHeight / 2} Z`}
            fill="url(#waveformGradient)"
            opacity="0.3"
          />

          {/* 渐变定义 */}
          <defs>
            <linearGradient id="waveformGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="#10b981" stopOpacity="0.6" />
              <stop offset="100%" stopColor="#10b981" stopOpacity="0.1" />
            </linearGradient>
          </defs>
        </svg>
      </div>

      {/* 网格线（可选） */}
      <div className="absolute inset-0 pointer-events-none">
        {Array.from({ length: Math.ceil(duration / 10) }, (_, i) => {
          const x = timeToPixel(i * 10) - scrollLeft;
          if (x >= 0 && x <= window.innerWidth) {
            return (
              <div
                key={i}
                className="absolute top-0 bottom-0 w-px bg-gray-700 opacity-30"
                style={{ left: x }}
              />
            );
          }
          return null;
        })}
      </div>
    </div>
  );
};

export default AudioTrack;
