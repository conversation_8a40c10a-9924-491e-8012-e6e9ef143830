# 视频文件目录

请将您的视频文件放置在此目录中。

## 当前配置

应用程序配置为加载以下视频文件：
- **文件名**: `1.mp4`
- **路径**: `public/1.mp4`
- **访问URL**: `/1.mp4` (React开发服务器) 或 `./public/1.mp4` (静态HTML)

## 使用说明

1. 将您的视频文件 `C:\Users\<USER>\Downloads\1.mp4` 复制到此目录
2. 确保文件名为 `1.mp4`
3. 启动开发服务器：`npm run dev`
4. 访问 `http://localhost:5173` 查看React应用
5. 或直接打开 `demo.html` 查看静态演示

## 支持的视频格式

- MP4 (推荐)
- WebM
- OGV
- 其他浏览器支持的视频格式

## 注意事项

- 确保视频文件不要太大，以免影响加载速度
- 如果更改视频文件名，需要同时更新代码中的路径引用
